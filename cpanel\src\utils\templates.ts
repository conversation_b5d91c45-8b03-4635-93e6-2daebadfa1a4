/**
 * Утилитарные функции для работы с шаблонами страниц
 */

import type {
  PageTemplate,
  TemplateKind,
  TemplateConfig,
  CategoryTemplateConfig,
  PartTemplateConfig,
  CatalogItemTemplateConfig,
  AttributeListConfig,
  TemplateFormState,
  TemplateFormErrors,
} from '@/types/templates';

export const TEMPLATE_KIND_FILTER_OPTIONS = [
  { label: 'Все типы', value: undefined },
  { label: 'Категория', value: 'CATEGORY' },
  { label: 'Запчасть', value: 'PART' },
  { label: 'Каталожная позиция', value: 'CATALOG_ITEM' },
];

export const TEMPLATE_KIND_OPTIONS = [
  { label: 'Категория', value: 'CATEGORY' },
  { label: 'Запчасть', value: 'PART' },
  { label: 'Каталожная позиция', value: 'CATALOG_ITEM' },
];

/**
 * Получить конфигурацию шаблона по его типу
 */
export function getConfigByKind(template: PageTemplate): TemplateConfig | null {
  switch (template.kind) {
    case 'CATEGORY':
      return template.categoryConfig || null;
    case 'PART':
      return template.partConfig || null;
    case 'CATALOG_ITEM':
      return template.catalogItemConfig || null;
    default:
      return null;
  }
}

/**
 * Создать конфигурацию по умолчанию для указанного типа шаблона
 */
export function createDefaultConfig(kind: TemplateKind): TemplateConfig {
  const defaultAttributeConfig: AttributeListConfig = {
    attributeNames: [],
    sortOrder: [],
    withUnits: false,
  };

  switch (kind) {
    case 'CATEGORY':
      return {
        h1: '',
        h2: '',
        description: '',
        footer: '',
        filters: { ...defaultAttributeConfig },
        productAttrs: { ...defaultAttributeConfig, withUnits: true },
      } as CategoryTemplateConfig;

    case 'PART':
      return {
        h1: '',
        h2: '',
        attributes: { ...defaultAttributeConfig, withUnits: true },
      } as PartTemplateConfig;

    case 'CATALOG_ITEM':
      return {
        h1: '',
        h2: '',
        attributes: { ...defaultAttributeConfig, withUnits: true },
      } as CatalogItemTemplateConfig;

    default:
      throw new Error(`Неизвестный тип шаблона: ${kind}`);
  }
}

/**
 * Создать начальное состояние формы для нового шаблона
 */
export function createInitialFormState(): TemplateFormState {
  return {
    name: '',
    description: '',
    kind: 'CATEGORY',
    partCategoryId: undefined,
    isActive: true,
    isDefault: false,
    categoryConfig: createDefaultConfig('CATEGORY') as CategoryTemplateConfig,
    partConfig: createDefaultConfig('PART') as PartTemplateConfig,
    catalogItemConfig: createDefaultConfig('CATALOG_ITEM') as CatalogItemTemplateConfig,
  };
}

/**
 * Преобразовать данные шаблона в состояние формы
 */
export function templateToFormState(template: PageTemplate): TemplateFormState {
  return {
    name: template.name || '',
    description: template.description || '',
    kind: template.kind,
    partCategoryId: template.partCategoryId || undefined,
    isActive: template.isActive,
    isDefault: template.isDefault,
    categoryConfig: template.categoryConfig || createDefaultConfig('CATEGORY') as CategoryTemplateConfig,
    partConfig: template.partConfig || createDefaultConfig('PART') as PartTemplateConfig,
    catalogItemConfig: template.catalogItemConfig || createDefaultConfig('CATALOG_ITEM') as CatalogItemTemplateConfig,
  };
}

/**
 * Валидировать состояние формы шаблона
 */
export function validateTemplateForm(form: TemplateFormState): TemplateFormErrors {
  const errors: TemplateFormErrors = {};

  // Базовая валидация
  if (!form.name.trim()) {
    errors.name = 'Название обязательно';
  } else if (form.name.length > 150) {
    errors.name = 'Название не должно превышать 150 символов';
  }

  if (form.description && form.description.length > 1000) {
    errors.description = 'Описание не должно превышать 1000 символов';
  }

  // Валидация в зависимости от типа шаблона
  switch (form.kind) {
    case 'CATEGORY':
      if (!form.categoryConfig.h1?.trim()) {
        errors.h1 = 'H1 заголовок обязателен для шаблона категории';
      }
      break;

    case 'PART':
      if (!form.partConfig.h1?.trim()) {
        errors.h1 = 'H1 заголовок обязателен для шаблона запчасти';
      }
      break;

    case 'CATALOG_ITEM':
      if (!form.catalogItemConfig.h1?.trim()) {
        errors.h1 = 'H1 заголовок обязателен для шаблона каталожной позиции';
      }
      break;
  }

  return errors;
}

/**
 * Проверить, есть ли ошибки валидации
 */
export function hasValidationErrors(errors: TemplateFormErrors): boolean {
  return Object.keys(errors).length > 0;
}

/**
 * Подготовить данные формы для отправки на сервер (создание)
 */
export function prepareCreateData(form: TemplateFormState) {
  const baseData = {
    name: form.name.trim(),
    description: form.description.trim() || undefined,
    kind: form.kind,
    partCategoryId: form.partCategoryId || undefined,
    isActive: form.isActive,
    isDefault: form.isDefault,
  };

  // Добавляем соответствующую конфигурацию
  switch (form.kind) {
    case 'CATEGORY':
      return {
        ...baseData,
        categoryConfig: form.categoryConfig,
      };

    case 'PART':
      return {
        ...baseData,
        partConfig: form.partConfig,
      };

    case 'CATALOG_ITEM':
      return {
        ...baseData,
        catalogItemConfig: form.catalogItemConfig,
      };

    default:
      return baseData;
  }
}

/**
 * Подготовить данные формы для отправки на сервер (обновление)
 */
export function prepareUpdateData(id: string, form: TemplateFormState) {
  return {
    id,
    ...prepareCreateData(form),
  };
}

/**
 * Получить активную конфигурацию для текущего типа шаблона
 */
export function getActiveConfig(form: TemplateFormState): TemplateConfig {
  switch (form.kind) {
    case 'CATEGORY':
      return form.categoryConfig;
    case 'PART':
      return form.partConfig;
    case 'CATALOG_ITEM':
      return form.catalogItemConfig;
    default:
      throw new Error(`Неизвестный тип шаблона: ${form.kind}`);
  }
}

/**
 * Обновить активную конфигурацию в состоянии формы
 */
export function updateActiveConfig(form: TemplateFormState, config: Partial<TemplateConfig>): TemplateFormState {
  const newForm = { ...form };

  switch (form.kind) {
    case 'CATEGORY':
      newForm.categoryConfig = { ...form.categoryConfig, ...config };
      break;
    case 'PART':
      newForm.partConfig = { ...form.partConfig, ...config };
      break;
    case 'CATALOG_ITEM':
      newForm.catalogItemConfig = { ...form.catalogItemConfig, ...config };
      break;
  }

  return newForm;
}

/**
 * Интерполировать шаблонную строку с данными
 */
export function interpolateTemplate(template: string, data: Record<string, any>): string {
  if (!template) return '';
  
  return template.replace(/\{\{\s*([^}]+)\s*\}\}/g, (_, expr) => {
    try {
      const path = expr.split('.');
      let current: any = data;
      
      for (const key of path) {
        current = current?.[key];
      }
      
      return current ?? '';
    } catch {
      return '';
    }
  });
}

/**
 * Получить отображаемое название типа шаблона
 */
export function getTemplateKindLabel(kind: TemplateKind): string {
  switch (kind) {
    case 'CATEGORY':
      return 'Категория';
    case 'PART':
      return 'Запчасть';
    case 'CATALOG_ITEM':
      return 'Каталожная позиция';
    default:
      return 'Неизвестный тип';
  }
}

/**
 * Проверить, является ли конфигурация валидной для указанного типа
 */
export function isValidConfigForKind(kind: TemplateKind, config: any): boolean {
  if (!config) return false;

  switch (kind) {
    case 'CATEGORY':
      return typeof config.h1 === 'string' && 
             typeof config.h2 === 'string' &&
             (config.filters === undefined || typeof config.filters === 'object') &&
             (config.productAttrs === undefined || typeof config.productAttrs === 'object');

    case 'PART':
    case 'CATALOG_ITEM':
      return typeof config.h1 === 'string' && 
             typeof config.h2 === 'string' &&
             typeof config.attributes === 'object' &&
             Array.isArray(config.attributes.attributeNames);

    default:
      return false;
  }
}

/**
 * Создать копию конфигурации атрибутов
 */
export function cloneAttributeListConfig(config: AttributeListConfig): AttributeListConfig {
  return {
    attributeNames: [...config.attributeNames],
    sortOrder: [...config.sortOrder],
    withUnits: config.withUnits,
  };
}
