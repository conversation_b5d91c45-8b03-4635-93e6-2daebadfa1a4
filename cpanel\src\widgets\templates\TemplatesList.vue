<template>
  <div class="w-full max-w-7xl">
    <VCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold">Шаблоны страниц</h2>
            <p class="text-sm text-gray-600 mt-1">
              Управление шаблонами для генерации страниц категорий, запчастей и
              каталожных позиций
            </p>
          </div>
          <VButton
            label="Создать шаблон"
            icon="Plus"
            @click="onCreate"
            class="shrink-0"
          />
        </div>
      </template>
      <template #content>
        <!-- Фильтры и поиск -->
        <div class="mb-6 space-y-4">
          <div class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-64">
              <VInputText
                v-model="filters.search"
                placeholder="Поиск по названию или описанию..."
                icon="Search"
                @input="onSearchInput"
                class="w-full"
              />
            </div>
            <VSelect
              v-model="filters.kind"
              :options="kindFilterOptions"
              placeholder="Тип шаблона"
              optionLabel="label"
              optionValue="value"
              class="w-48"
              @change="onFilterChange"
            />
            <VAutoComplete
              v-model="categoryQuery"
              :suggestions="categorySuggestions"
              optionLabel="name"
              placeholder="Категория"
              class="w-64"
              @complete="onCompleteCategory"
              @item-select="onSelectCategory"
              @clear="onClearCategory"
            />
            <VSelect
              v-model="filters.isActive"
              :options="activeFilterOptions"
              placeholder="Статус"
              optionLabel="label"
              optionValue="value"
              class="w-32"
              @change="onFilterChange"
            />
            <VButton
              label="Сбросить"
              icon="X"
              variant="outline"
              @click="resetFilters"
              v-if="hasActiveFilters"
            />
          </div>
        </div>

        <!-- Состояние загрузки -->
        <div v-if="loading" class="flex justify-center py-12">
          <div class="text-gray-500">Загрузка шаблонов...</div>
        </div>

        <!-- Ошибка загрузки -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-600 mb-4">{{ error }}</div>
          <VButton label="Повторить" icon="RefreshCw" @click="loadTemplates" />
        </div>

        <!-- Пустое состояние -->
        <div v-else-if="!templates.length" class="text-center py-12">
          <div class="text-gray-500 mb-4">
            {{
              hasActiveFilters ? "Шаблоны не найдены" : "Нет созданных шаблонов"
            }}
          </div>
          <VButton
            v-if="!hasActiveFilters"
            label="Создать первый шаблон"
            icon="Plus"
            @click="onCreate"
          />
        </div>

        <!-- Таблица шаблонов -->
        <VDataTable
          v-else
          :value="templates"
          dataKey="id"
          :paginator="true"
          :rows="pageSize"
          :totalRecords="totalCount"
          :lazy="true"
          @page="onPageChange"
          :loading="loading"
          class="w-full"
        >
          <VColumn field="name" header="Название" sortable>
            <template #body="{ data }">
              <div>
                <div class="font-medium">{{ data.name }}</div>
                <div v-if="data.description" class="text-sm text-gray-600 mt-1">
                  {{ truncateText(data.description, 100) }}
                </div>
              </div>
            </template>
          </VColumn>

          <VColumn field="kind" header="Тип" sortable>
            <template #body="{ data }">
              <VBadge
                :label="getKindLabel(data.kind)"
                :severity="getKindSeverity(data.kind)"
              />
            </template>
          </VColumn>

          <VColumn field="partCategoryId" header="Категория">
            <template #body="{ data }">
              <span v-if="data.partCategory" class="text-sm">
                {{ data.partCategory.name }}
              </span>
              <span v-else class="text-gray-400 text-sm">—</span>
            </template>
          </VColumn>

          <VColumn field="isDefault" header="По умолчанию">
            <template #body="{ data }">
              <VBadge v-if="data.isDefault" label="Да" severity="success" />
              <span v-else class="text-gray-400">—</span>
            </template>
          </VColumn>

          <VColumn field="isActive" header="Статус">
            <template #body="{ data }">
              <VBadge
                :label="data.isActive ? 'Активен' : 'Неактивен'"
                :severity="data.isActive ? 'success' : 'secondary'"
              />
            </template>
          </VColumn>

          <VColumn field="updatedAt" header="Обновлен" sortable>
            <template #body="{ data }">
              <span class="text-sm text-gray-600">
                {{ formatDate(data.updatedAt) }}
              </span>
            </template>
          </VColumn>

          <VColumn header="Действия" class="w-48">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <VButton
                  outlined
                  size="small"
                  @click="() => onPreview(data)"
                  v-tooltip="'Предпросмотр'"
                  ><EyeIcon class="w-4 h-4"
                /></VButton>
                <VButton
                  outlined
                  size="small"
                  @click="onEdit(data)"
                  v-tooltip="'Редактировать'"
                  ><PencilIcon class="w-4 h-4"
                /></VButton>
                <VButton
                  outlined
                  size="small"
                  @click="onDuplicate(data)"
                  v-tooltip="'Дублировать'"
                  ><CopyIcon class="w-4 h-4"
                /></VButton>
                <DangerButton
                  size="small"
                  outlined
                  @click="onDelete(data)"
                  :loading="deletingIds.has(data.id)"
                  v-tooltip="'Удалить'"
                >
                  <Trash2 class="w-4 h-4" />
                </DangerButton>
              </div>
            </template>
          </VColumn>
        </VDataTable>
      </template>
    </VCard>

    <!-- Диалог подтверждения удаления -->
    <VConfirmDialog
      v-model:visible="deleteDialog.visible"
      :header="`Удалить шаблон '${deleteDialog.template?.name}'?`"
      message="Это действие нельзя отменить. Шаблон будет удален навсегда."
      icon="AlertTriangle"
      acceptLabel="Удалить"
      rejectLabel="Отмена"
      acceptClass="p-button-danger"
      @accept="confirmDelete"
      @reject="cancelDelete"
    />

    <!-- Диалог предпросмотра -->
    <VDialog
      v-model:visible="previewDialog.visible"
      :header="`Предпросмотр: ${previewDialog.template?.name || 'Шаблон'}`"
      modal
      :style="{ width: '90vw', maxWidth: '1200px' }"
      @hide="closePreviewDialog"
    >
      <template #content>
        <div v-if="previewDialog.loading" class="flex justify-center py-12">
          <div class="text-gray-500">Загрузка предпросмотра...</div>
        </div>

        <div v-else-if="!previewDialog.previewData" class="text-center py-12">
          <div class="text-red-600 mb-4">Не удалось загрузить данные для предпросмотра</div>
          <VButton label="Повторить" icon="RefreshCw" @click="onPreview(previewDialog.template!)" />
        </div>

        <div v-else class="text-center py-8">
          <h3 class="text-lg font-semibold mb-4">Предпросмотр шаблона</h3>
          <p class="text-gray-600 mb-4">{{ previewDialog.template?.name }}</p>
          <div class="bg-gray-50 p-4 rounded-lg text-left">
            <h4 class="font-medium mb-2">Тип: {{ previewDialog.template?.kind }}</h4>
            <p class="text-sm text-gray-600">ID: {{ previewDialog.template?.id }}</p>
          </div>
        </div>

      </template>

      <template #footer>
        <VButton
          label="Закрыть"
          icon="X"
          variant="outline"
          @click="closePreviewDialog"
        />
      </template>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VSelect from "@/volt/Select.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import VDataTable from "@/volt/DataTable.vue";
import VColumn from "primevue/column";
import VInputText from "@/volt/InputText.vue";
import VBadge from "@/volt/Badge.vue";
import VConfirmDialog from "@/volt/ConfirmDialog.vue";
import VDialog from "@/volt/Dialog.vue";
import PreviewRenderer from "./components/PreviewRenderer.vue";
import { navigate } from "astro:transitions/client";
import { useTrpc } from "@/composables/useTrpc";
import type {
  PageTemplate,
  TemplateKind,
  TemplateListFilters,
  PartCategory,
  TemplatePreviewData,
  CategoryTemplateConfig,
  PartTemplateConfig,
  CatalogItemTemplateConfig,
} from "@/types/templates";
import {
  TEMPLATE_KIND_FILTER_OPTIONS,
  getTemplateKindLabel,
  getActiveConfig,
} from "@/utils/templates";
import { EyeIcon } from "lucide-vue-next";
import { PencilIcon } from "lucide-vue-next";
import { CopyIcon } from "lucide-vue-next";
import DangerButton from "@/volt/DangerButton.vue";
import { Trash2 } from "lucide-vue-next";

const trpc = useTrpc();

// Состояние компонента
const loading = ref(false);
const error = ref<string | null>(null);
const templates = ref<PageTemplate[]>([]);
const totalCount = ref(0);
const currentPage = ref(0);
const pageSize = ref(20);
const deletingIds = ref(new Set<string>());

// Фильтры и поиск
const filters = ref<TemplateListFilters>({});
const categoryQuery = ref("");
const categorySuggestions = ref<PartCategory[]>([]);
const searchTimeout = ref<NodeJS.Timeout | null>(null);

// Диалог удаления
const deleteDialog = ref({
  visible: false,
  template: null as PageTemplate | null,
});

// Диалог предпросмотра
const previewDialog = ref({
  visible: false,
  loading: false,
  template: null as PageTemplate | null,
  previewData: null as TemplatePreviewData | null,
});

// Опции для селектов
const kindFilterOptions = TEMPLATE_KIND_FILTER_OPTIONS;

const activeFilterOptions = [
  { label: "Все", value: undefined },
  { label: "Активные", value: true },
  { label: "Неактивные", value: false },
];

// Вычисляемые свойства
const hasActiveFilters = computed(() => {
  return !!(
    filters.value.search ||
    filters.value.kind ||
    filters.value.partCategoryId !== undefined ||
    filters.value.isActive !== undefined
  );
});

// Методы загрузки данных
async function loadTemplates() {
  try {
    loading.value = true;
    error.value = null;

    const queryParams = {
      ...filters.value,
      skip: currentPage.value * pageSize.value,
      take: pageSize.value,
    };

    const data = await trpc.client.pageTemplates.list.query(queryParams);
    templates.value = data || [];

    // Если есть пагинация в ответе, используем её
    if (typeof data === "object" && "items" in data) {
      templates.value = data.items || [];
      totalCount.value = data.total || 0;
    } else {
      // Иначе считаем что получили все записи
      totalCount.value = templates.value.length;
    }
  } catch (err: any) {
    error.value = err?.message || "Ошибка загрузки шаблонов";
    templates.value = [];
    totalCount.value = 0;
  } finally {
    loading.value = false;
  }
}

// Обработчики событий
function onSearchInput() {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  searchTimeout.value = setTimeout(() => {
    currentPage.value = 0;
    loadTemplates();
  }, 500);
}

function onFilterChange() {
  currentPage.value = 0;
  loadTemplates();
}

function onPageChange(event: any) {
  currentPage.value = event.page;
  loadTemplates();
}

function resetFilters() {
  filters.value = {};
  categoryQuery.value = "";
  currentPage.value = 0;
  loadTemplates();
}

// Работа с категориями
async function onCompleteCategory(e: any) {
  try {
    const res = await trpc.client.crud.partCategory.findMany.query({
      where: { name: { contains: e.query } },
      take: 10,
    });
    categorySuggestions.value = res || [];
  } catch (error) {
    console.error("Ошибка загрузки категорий:", error);
    categorySuggestions.value = [];
  }
}

function onSelectCategory(e: any) {
  filters.value.partCategoryId = e.value?.id;
  onFilterChange();
}

function onClearCategory() {
  filters.value.partCategoryId = undefined;
  categoryQuery.value = "";
  onFilterChange();
}

// CRUD операции
function onCreate() {
  navigate("/admin/templates/new");
}

function onEdit(template: PageTemplate) {
  navigate(`/admin/templates/${template.id}`);
}

async function onPreview(template: PageTemplate) {
  try {
    previewDialog.value.loading = true;
    previewDialog.value.template = template;
    previewDialog.value.visible = true;
    previewDialog.value.previewData = { template, data: {}, attr: { byName: {} } };

    // Имитация загрузки данных
    await new Promise(resolve => setTimeout(resolve, 500));
  } catch (error) {
    console.error('Ошибка при предпросмотре шаблона:', error);
  } finally {
    previewDialog.value.loading = false;
  }
}

function closePreviewDialog() {
  previewDialog.value.visible = false;
  previewDialog.value.template = null;
  previewDialog.value.previewData = null;
}

async function onDuplicate(template: PageTemplate) {
  try {
    const duplicateData = {
      name: `${template.name} (копия)`,
      description: template.description,
      kind: template.kind,
      partCategoryId: template.partCategoryId,
      isActive: false, // Копия создается неактивной
      isDefault: false, // Копия не может быть по умолчанию
      categoryConfig: template.categoryConfig,
      partConfig: template.partConfig,
      catalogItemConfig: template.catalogItemConfig,
    };

    const result = await trpc.client.pageTemplates.create.mutate(duplicateData);

    if (result) {
      await loadTemplates();
      // Показываем уведомление об успехе через trpc.execute
      trpc.execute(() => Promise.resolve(result), {
        success: { title: "Успешно", message: "Шаблон продублирован" },
      });
    }
  } catch (error) {
    console.error("Ошибка дублирования шаблона:", error);
  }
}

function onDelete(template: PageTemplate) {
  deleteDialog.value.template = template;
  deleteDialog.value.visible = true;
}

async function confirmDelete() {
  const template = deleteDialog.value.template;
  if (!template) return;

  try {
    deletingIds.value.add(template.id);

    await trpc.client.pageTemplates.delete.mutate({ id: template.id });

    await loadTemplates();

    // Показываем уведомление об успехе
    trpc.execute(() => Promise.resolve(true), {
      success: { title: "Удалено", message: "Шаблон успешно удален" },
    });
  } catch (error) {
    console.error("Ошибка удаления шаблона:", error);
  } finally {
    deletingIds.value.delete(template.id);
    cancelDelete();
  }
}

function cancelDelete() {
  deleteDialog.value.visible = false;
  deleteDialog.value.template = null;
}

// Утилитарные функции
function getKindLabel(kind: TemplateKind): string {
  return getTemplateKindLabel(kind);
}

function getKindSeverity(kind: TemplateKind): string {
  switch (kind) {
    case "CATEGORY":
      return "info";
    case "PART":
      return "success";
    case "CATALOG_ITEM":
      return "warning";
    default:
      return "secondary";
  }
}

function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
}

function formatDate(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleDateString("ru-RU", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// Инициализация
onMounted(() => {
  loadTemplates();
});

// Отслеживание изменений фильтров
watch(
  () => filters.value,
  () => {
    if (searchTimeout.value) {
      clearTimeout(searchTimeout.value);
    }
  },
  { deep: true }
);
</script>
